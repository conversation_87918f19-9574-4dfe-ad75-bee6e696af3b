# CodeAct Agent

A production-ready Python code execution agent with Google's A2A protocol integration, persistent variable state, and containerized deployment.

## Overview

CodeAct is a LangGraph-based agent that executes Python code while maintaining variable persistence across conversation turns. It implements Google's Agent-to-Agent (A2A) protocol for seamless integration into multi-agent orchestration systems.

**Key Features:**
- **Code Execution**: Execute Python code in isolated conda/virtual environments
- **Variable Persistence**: Variables persist across turns within the same conversation
- **A2A Protocol**: Full Google A2A compliance for multi-agent systems
- **Streaming Support**: Real-time code execution results
- **MCP Integration**: Extensible tool system (web fetch, file operations)
- **Production Ready**: Docker containerization with health checks

## Quick Start

### Docker Deployment (Recommended)

1. **Setup environment:**
   ```bash
   git clone <repository>
   cd agent-codeact
   cp .env.sample .env
   # Add your GOOGLE_API_KEY to .env
   ```

2. **Start the agent:**
   ```bash
   docker compose up -d
   ```

3. **Verify deployment:**
   ```bash
   curl http://localhost:8000/health
   curl http://localhost:8000/rpc/.well-known/agent.json
   ```

### Local Development

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Create conda environment (recommended):**
   ```bash
   conda create -n codeact-test python=3.11 numpy pandas matplotlib seaborn scikit-learn -y
   export CODEACT_CONDA_ENV=codeact-test
   ```

3. **Run the server:**
   ```bash
   python codeact_a2a_server.py
   ```

## Architecture

### Core Components

- **CodeActExecutor**: Handles code execution with variable persistence
- **A2A Integration**: Implements streaming, task management, and protocol compliance
- **Environment Manager**: Supports conda environments, virtual environments, or direct execution
- **MCP Tools**: Context7 integration for web access and documentation lookup

### Execution Flow

1. **Message Reception**: A2A protocol receives code execution request
2. **Context Retrieval**: Load persistent variables for the conversation
3. **Code Execution**: Run code in isolated environment with context
4. **Variable Persistence**: Store new/modified variables for future turns
5. **Streaming Response**: Return results with A2A protocol formatting

## Usage Examples

### A2A Client Integration

```python
import asyncio
import httpx
from a2a.client import A2AClient
from a2a.types import MessageSendParams, SendMessageRequest

async def execute_code(code: str):
    async with httpx.AsyncClient() as client:
        agent = await A2AClient.get_client_from_agent_card_url(
            client, "http://localhost:8000/rpc/"
        )
        
        request = SendMessageRequest(params=MessageSendParams(
            message={
                "role": "user",
                "parts": [{"kind": "text", "text": code}],
                "messageId": "unique-id",
            },
            contextId="conversation-1"
        ))
        
        response = await agent.send_message(request)
        return response

# Execute code with variable persistence
result1 = await execute_code("data = [1, 2, 3, 4, 5]\nprint(f'Created: {data}')")
result2 = await execute_code("total = sum(data)\nprint(f'Sum: {total}')")  # Uses 'data' from previous turn
```

### Multi-Turn Conversations

Variables persist within the same `contextId`:

```python
# Turn 1: Data preparation
"import pandas as pd\ndf = pd.DataFrame({'values': [1, 2, 3, 4, 5]})\nprint(df.head())"

# Turn 2: Data analysis (same contextId)
"mean_val = df['values'].mean()\nprint(f'Mean: {mean_val}')"

# Turn 3: Visualization (same contextId)
"import matplotlib.pyplot as plt\nplt.plot(df['values'])\nplt.savefig('/tmp/plot.png')\nprint('Plot saved')"
```

### Environment Execution

The agent supports multiple execution environments:

```python
# Conda environment (recommended)
export CODEACT_CONDA_ENV=codeact-test

# Virtual environment
export CODEACT_VENV_PATH=/path/to/venv

# Direct execution (fallback)
# No environment variables set
```

## API Reference

### Health Endpoint
```bash
GET /health
# Response: "CodeAct A2A server is running"
```

### Agent Card
```bash
GET /rpc/.well-known/agent.json
# Response: JSON with agent capabilities and skills
```

### A2A RPC Endpoint
```bash
POST /rpc/
Content-Type: application/json

{
  "jsonrpc": "2.0",
  "method": "sendMessage",
  "params": {
    "message": {
      "role": "user",
      "parts": [{"kind": "text", "text": "print('Hello World')"}],
      "messageId": "msg-1"
    },
    "contextId": "conversation-1"
  },
  "id": "req-1"
}
```

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `GOOGLE_API_KEY` | Required | Google Gemini API key |
| `A2A_SERVER_HOST` | `0.0.0.0` | Server bind address |
| `A2A_SERVER_PORT` | `8000` | Server port |
| `A2A_PATH_PREFIX` | `/rpc` | API path prefix |
| `A2A_PUBLIC_URL` | Auto-generated | External URL for agent card |
| `CODEACT_CONDA_ENV` | None | Conda environment name |
| `CODEACT_VENV_PATH` | None | Virtual environment path |

### Docker Configuration

The Docker image includes:
- Python 3.13 runtime
- Conda with pre-installed data science packages
- Node.js and Deno for JavaScript execution
- UV package manager for fast Python installs

Pre-configured conda environments:
- `codeact-test`: numpy, pandas, matplotlib, seaborn, scikit-learn, requests, beautifulsoup4, plotly
- `codeact-data`: Additional packages for data analysis (jupyter, sqlalchemy, psycopg2-binary, pymongo, redis)

## Testing

### Single Command Testing

**Run all tests:**
```bash
# Using Python test runner
python run_tests.py all

# Using make (recommended)
make test

# Inside Docker container  
./test_in_docker.sh all
```

**Run specific test suites:**
```bash
# Environment tests
python run_tests.py env
make test-env

# Package installation tests
python run_tests.py packages
make test-packages

# A2A protocol tests (requires running server)
python run_tests.py a2a
make test-a2a

# Docker integration tests
python run_tests.py docker
make test-docker
```

### Complete Clean Test

```bash
# Full clean restart and test
make clean-test
```

### Test Suites

1. **Environment Tests**: Conda/venv execution, package availability
2. **Package Installation Tests**: Dynamic pip/conda/R package installation
3. **A2A Protocol Tests**: Streaming, task management, variable persistence
4. **Docker Integration Tests**: Health checks, container functionality

## Multi-Agent Integration

CodeAct can be integrated into larger multi-agent systems:

```python
# Example multi-agent setup
agents = {
    "codeact": "http://localhost:8000/rpc/",
    "researcher": "http://localhost:8001/rpc/",
    "planner": "http://localhost:8002/rpc/"
}

# Coordinate between agents
research_result = await researcher.send_message(query)
code_solution = await codeact.send_message(f"Implement: {research_result}")
plan = await planner.send_message(f"Create plan for: {code_solution}")
```

## Production Deployment

### Docker Compose (Recommended)

```yaml
services:
  codeact:
    build: .
    ports:
      - "8000:8000"
    environment:
      - CODEACT_CONDA_ENV=codeact-test
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
    volumes:
      - /tmp:/tmp
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: codeact-agent
spec:
  replicas: 3
  selector:
    matchLabels:
      app: codeact-agent
  template:
    metadata:
      labels:
        app: codeact-agent
    spec:
      containers:
      - name: codeact
        image: codeact-agent:latest
        ports:
        - containerPort: 8000
        env:
        - name: GOOGLE_API_KEY
          valueFrom:
            secretKeyRef:
              name: api-keys
              key: google-api-key
        - name: CODEACT_CONDA_ENV
          value: "codeact-test"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 60
          periodSeconds: 30
```

### Performance Considerations

- **Startup Time**: ~45 seconds for full initialization (MCP connections, conda loading)
- **Memory Usage**: ~2GB with conda environments and packages loaded
- **Concurrent Requests**: Supports multiple simultaneous conversations with isolated variable contexts
- **Scaling**: Stateless design allows horizontal scaling with shared conversation store

## Troubleshooting

### Common Issues

1. **Health check failures**: Server needs 45+ seconds to fully initialize
2. **Variable persistence not working**: Ensure same `contextId` across turns
3. **Package import errors**: Verify conda environment setup and package installation
4. **Connection refused**: Check server binding (should be `0.0.0.0` in containers)

### Debug Commands

```bash
# Check container logs
docker compose logs codeact

# Test environment inside container
docker compose exec codeact conda list -n codeact-test

# Manual health check
curl -v http://localhost:8000/health

# Test A2A endpoint
curl -X POST http://localhost:8000/rpc/ \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"sendMessage","params":{"message":{"role":"user","parts":[{"kind":"text","text":"print(\"test\")"}],"messageId":"1"},"contextId":"test"},"id":"1"}'
```

## Full reset and running tests

```bash
# 1. Complete clean restart
docker compose down --volumes --remove-orphans
docker system prune -f  
docker compose build --no-cache
docker compose up -d
sleep 50

# 2. Test health
curl http://localhost:8000/health

# 3. Test everything
# ✅ Environment tests
python run_tests.py env    
# ✅ Package tests  
python run_tests.py packages  
# Now should work with conftest.py fixture
python run_tests.py a2a    
```

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- GitHub Issues: [Repository Issues](repository-url)
- Documentation: [Agent Documentation](docs-url)
- A2A Protocol: [Google A2A Specification](a2a-spec-url)
