#!/bin/bash
# Run tests inside Docker container

echo "🧪 Running tests inside Docker container..."

# Function to run command in container
run_in_container() {
    docker compose exec -T codeact "$@"
}

# Check if container is running
if ! docker compose ps | grep -q "Up"; then
    echo "Starting container..."
    docker compose up -d
    sleep 30
fi

# Run tests based on argument
if [ "$1" = "all" ]; then
    echo "Running all tests..."
    run_in_container python run_tests.py all
elif [ "$1" = "env" ]; then
    echo "Running environment tests..."
    run_in_container python run_tests.py env
elif [ "$1" = "packages" ]; then
    echo "Running package tests..."
    run_in_container python run_tests.py packages
elif [ "$1" = "a2a" ]; then
    echo "Running A2A tests..."
    run_in_container python run_tests.py a2a
else
    echo "Running all tests..."
    run_in_container python run_tests.py all
fi

echo "✅ Docker tests completed"
