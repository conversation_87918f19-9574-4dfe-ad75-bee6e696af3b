"""
Enhanced A2A Server for CodeAct Agent with persistent variable state
This server integrates your CodeAct LangGraph agent with Google's A2A protocol,
allowing it to be used in multi-agent orchestration platforms.
"""

import asyncio
import uuid
import os
import uvicorn
import httpx
from typing import Any, AsyncIterable, Dict
import logging

# Configure logging
logging.basicConfig(level=logging.INFO,
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from a2a.server.apps import A2AStarletteApplication
from a2a.server.agent_execution import AgentExecutor, RequestContext
from a2a.server.events.event_queue import EventQueue
from a2a.server.request_handlers import <PERSON><PERSON>ultRequestHand<PERSON>
from a2a.server.tasks.inmemory_task_store import InMemoryTaskStore
from a2a.server.tasks.inmemory_push_notifier import InMemoryPushNotifier
from a2a.types import (
    AgentCard,
    AgentCapabilities,
    AgentSkill,
    TextPart,
    Part,
    TaskStatusUpdateEvent,
    TaskStatus,
    TaskState,
    TaskArtifactUpdateEvent,
    Artifact,
    Task,
    Role,
    Message
)

from langchain_core.messages import HumanMessage
from starlette.applications import Starlette
from starlette.routing import Mount, Route
from starlette.responses import JSONResponse, Response

from codeact_graph import build_agent, eval  # import your evaluation function

# ────────── Agent Card Definition ──────────
SKILL = AgentSkill(
    id="codeact",
    name="CodeAct",
    description="Python code execution agent with LangGraph",
    tags=["langgraph", "code", "python"],
    inputModes=["text/plain"],
    outputModes=["text/plain"],
)

PATH_PREFIX = os.getenv("A2A_PATH_PREFIX", "/rpc")
# Make sure PATH_PREFIX always ends with a slash
if not PATH_PREFIX.endswith("/"):
    PATH_PREFIX += "/"

SERVER_HOST = os.getenv("A2A_SERVER_HOST", "0.0.0.0")
SERVER_PORT = int(os.getenv("A2A_SERVER_PORT", "8000"))
PUBLIC_URL = os.getenv("A2A_PUBLIC_URL", f"http://localhost:{SERVER_PORT}")

AGENT_CARD = AgentCard(
    name="CodeAct Agent",
    description="LangGraph-based Python code execution agent exposed via A2A protocol",
    version="1.0.0",
    url=f"{PUBLIC_URL}{PATH_PREFIX}",
    capabilities=AgentCapabilities(streaming=True),
    skills=[SKILL],
    defaultInputModes=["text/plain"],
    defaultOutputModes=["text/plain"],
)

# ───── CodeAct Agent Executor ─────
class CodeActExecutor(AgentExecutor):
    """
    Agent executor for CodeAct LangGraph agent that handles streaming
    responses and properly formats events for the A2A protocol.
    """

    def __init__(self):
        logger.info("Initializing CodeAct agent...")
        self.graph, self.mcp_tools = asyncio.run(build_agent())
        # Store variable contexts between executions for each conversation
        self.conversation_contexts: Dict[str, Dict[str, Any]] = {}
        logger.info(f"CodeAct agent initialized with {len(self.mcp_tools)} MCP tools")
        logger.info(f"Using A2A URL: {PUBLIC_URL}{PATH_PREFIX}")

    async def execute(
        self,
        context: RequestContext,
        event_queue: EventQueue,
    ) -> None:
        query = context.get_user_input()
        task = context.current_task

        if not context.message:
            raise Exception('No message provided')

        # IMPORTANT: Use the task_id and context_id provided by the A2A framework
        task_id = context.task_id
        context_id = context.context_id

        logger.info(f"Executing task {task_id} in context {context_id} with query: {query[:50]}...")

        # Send initial "working" status
        event_queue.enqueue_event(
            TaskStatusUpdateEvent(
                contextId=context_id,
                taskId=task_id,
                status=TaskStatus(
                    state=TaskState.working,
                    message=Message(
                        role=Role.agent,
                        parts=[Part(root=TextPart(text="Processing your code request..."))],
                        messageId=str(uuid.uuid4()),
                    ),
                ),
                final=False,
            )
        )

        # Process through the agent and stream results
        async for chunk in self.stream_from_agent(query, task_id, context_id):
            # If it's an intermediate chunk, update status with working
            if not chunk["is_complete"]:
                event_queue.enqueue_event(
                    TaskStatusUpdateEvent(
                        contextId=context_id,
                        taskId=task_id,
                        status=TaskStatus(
                            state=TaskState.working,
                            message=Message(
                                role=Role.agent,
                                parts=[Part(root=TextPart(text=chunk["content"]))],
                                messageId=str(uuid.uuid4()),
                            ),
                        ),
                        final=False,
                    )
                )
            else:
                # If this is the final chunk, handle completion and potential input request
                if chunk.get("require_user_input", False):
                    # If agent needs more input (like follow-up questions or clarification)
                    event_queue.enqueue_event(
                        TaskStatusUpdateEvent(
                            contextId=context_id,
                            taskId=task_id,
                            status=TaskStatus(
                                state=TaskState.input_required,
                                message=Message(
                                    role=Role.agent,
                                    parts=[Part(root=TextPart(text=chunk["content"]))],
                                    messageId=str(uuid.uuid4()),
                                ),
                            ),
                            final=True,
                        )
                    )
                else:
                    # Store any new variables that were created during execution
                    if "new_vars" in chunk:
                        # Update the conversation context with new variables
                        if context_id not in self.conversation_contexts:
                            self.conversation_contexts[context_id] = {}

                        # Update with new variables
                        self.conversation_contexts[context_id].update(chunk["new_vars"])
                        logger.info(f"Updated context {context_id} with {len(chunk['new_vars'])} new variables")

                    # The task is complete
                    # First send the final artifact
                    event_queue.enqueue_event(
                        TaskArtifactUpdateEvent(
                            contextId=context_id,
                            taskId=task_id,
                            artifact=Artifact(
                                artifactId=str(uuid.uuid4()),
                                parts=[Part(root=TextPart(text=chunk["content"]))],
                            ),
                            append=False,
                            lastChunk=True,
                        )
                    )

                    # Then mark the task as completed
                    event_queue.enqueue_event(
                        TaskStatusUpdateEvent(
                            contextId=context_id,
                            taskId=task_id,
                            status=TaskStatus(state=TaskState.completed),
                            final=True,
                        )
                    )

                    logger.info(f"Task {task_id} completed successfully")

    async def stream_from_agent(self, query: str, thread_id: str, context_id: str) -> AsyncIterable[dict[str, Any]]:
        """
        Stream results from the CodeAct agent using LangGraph's native streaming.

        Args:
            query: The user's input query
            thread_id: Session ID for maintaining context between calls
            context_id: Conversation context ID for variable persistence

        Yields:
            Dictionaries with response chunks and metadata
        """
        logger.info(f"Starting LangGraph CodeAct streaming for query: {query[:50]}...")

        try:
            # First chunk - acknowledgement
            yield {
                "is_complete": False,
                "require_user_input": False,
                "content": "🚀 Starting CodeAct agent execution..."
            }

            # Import LangChain message types
            from langchain_core.messages import HumanMessage

            # Create the message for the agent
            messages = [HumanMessage(content=query)]

            # Configure the agent with the thread_id for conversation persistence
            config = {"configurable": {"thread_id": thread_id}}

            logger.info(f"Invoking LangGraph CodeAct agent with config: {config}")

            # Use LangGraph's native streaming with proper stream_mode
            accumulated_content = ""
            final_result = None

            async for typ, chunk in self.graph.astream(
                {"messages": messages},
                stream_mode=["values", "messages"],
                config=config
            ):
                if typ == "messages":
                    # Stream token-by-token output from the agent
                    if hasattr(chunk[0], 'content') and chunk[0].content:
                        content = str(chunk[0].content)
                        if content and content != accumulated_content:
                            # Send incremental content
                            new_content = content[len(accumulated_content):]
                            if new_content.strip():
                                yield {
                                    "is_complete": False,
                                    "require_user_input": False,
                                    "content": new_content
                                }
                                accumulated_content = content

                elif typ == "values":
                    # This contains the final state
                    final_result = chunk
                    logger.info(f"Received final values from LangGraph: {type(chunk)}")

            # Process the final result
            if final_result and isinstance(final_result, dict) and "messages" in final_result:
                final_messages = final_result["messages"]
                if final_messages and len(final_messages) > 0:
                    final_message = final_messages[-1]
                    if hasattr(final_message, 'content'):
                        final_content = str(final_message.content)

                        # If we haven't streamed this content yet, send it
                        if final_content != accumulated_content:
                            remaining_content = final_content[len(accumulated_content):]
                            if remaining_content.strip():
                                yield {
                                    "is_complete": False,
                                    "require_user_input": False,
                                    "content": remaining_content
                                }

                        # Send final completion
                        yield {
                            "is_complete": True,
                            "require_user_input": False,
                            "content": "✅ CodeAct execution completed successfully!",
                            "new_vars": {}  # TODO: Extract variables from execution context
                        }
                    else:
                        logger.warning(f"Final message has no content: {final_message}")
                        yield {
                            "is_complete": True,
                            "require_user_input": False,
                            "content": "Task completed but no content returned.",
                            "new_vars": {}
                        }
                else:
                    logger.warning("No messages in final result")
                    yield {
                        "is_complete": True,
                        "require_user_input": False,
                        "content": "Task completed but no messages returned.",
                        "new_vars": {}
                    }
            else:
                logger.warning(f"Unexpected final result format: {final_result}")
                yield {
                    "is_complete": True,
                    "require_user_input": False,
                    "content": f"Task completed. Result: {str(final_result)[:200]}..." if final_result else "Task completed with no result.",
                    "new_vars": {}
                }

            logger.info("LangGraph CodeAct streaming completed successfully")

        except Exception as e:
            logger.error(f"Error in LangGraph CodeAct streaming: {str(e)}", exc_info=True)
            yield {
                "is_complete": True,
                "require_user_input": False,
                "content": f"❌ Error executing CodeAct agent: {str(e)}"
            }

    async def cancel(self, context: RequestContext, event_queue: EventQueue) -> None:
        """Handle task cancellation requests."""
        if not context.task_id:
            logger.warning("Task cancellation requested but no task_id provided")
            return

        logger.info(f"Cancelling task {context.task_id}")

        # Notify the user that cancellation is in progress
        event_queue.enqueue_event(
            TaskStatusUpdateEvent(
                contextId=context.context_id,
                taskId=context.task_id,
                status=TaskStatus(
                    state=TaskState.canceled,
                    message=Message(
                        role=Role.agent,
                        parts=[Part(root=TextPart(text="Task has been canceled."))],
                        messageId=str(uuid.uuid4()),
                    ),
                ),
                final=True,
            )
        )

# Helper endpoint that simply returns the agent card
async def get_agent_card(request):
    logger.info(f"Agent card requested from {request.url}")
    return JSONResponse(AGENT_CARD.model_dump(mode="json", exclude_none=True))

# An endpoint to check server status
async def health_check(request):
    return Response("CodeAct A2A server is running")

# ───── Starlette app with A2A server ─────
def build_app():
    """
    Create the Starlette application with A2A server mounted at PATH_PREFIX.

    Returns:
        A Starlette application instance
    """
    http_handler = DefaultRequestHandler(
        agent_executor=CodeActExecutor(),
        task_store=InMemoryTaskStore(),
        push_notifier=InMemoryPushNotifier(httpx.AsyncClient()),
    )

    # Create A2A application with proper routing and explicit URL paths
    inner_app = A2AStarletteApplication(
        agent_card=AGENT_CARD,
        http_handler=http_handler,
    ).build(
        # Specify exact URLs to avoid redirect issues
        rpc_url="/",
        agent_card_url="/.well-known/agent.json",
    )

    # Clean up PATH_PREFIX for mounting
    mount_path = PATH_PREFIX.rstrip("/")
    if not mount_path:
        mount_path = "/"

    # Add additional routes for status and direct agent card access
    # NOTE: Order matters! Specific routes must come before Mount
    routes = [
        Route("/health", health_check, methods=["GET"]),
        Route("/agent-card", get_agent_card, methods=["GET"]),
        # Add agent card at root level for discovery
        Route("/.well-known/agent.json", get_agent_card, methods=["GET"]),
        Mount(mount_path, app=inner_app),
    ]

    logger.info(f"Registering routes: {[str(r) for r in routes]}")
    logger.info(f"Mount path: {mount_path}")

    return Starlette(routes=routes)

# Main entry point
if __name__ == "__main__":
    print(f"Starting CodeAct A2A server at {PUBLIC_URL}{PATH_PREFIX}")
    print(f"Agent card available at {PUBLIC_URL}{PATH_PREFIX}.well-known/agent.json")
    print(f"Health check endpoint at {PUBLIC_URL}/health")

    uvicorn.run(
        build_app(),
        host=SERVER_HOST,
        port=SERVER_PORT,
        # Add these options for better logging and behavior
        log_level="info",
        access_log=True,
    )