#!/bin/bash

# Test script for standalone CodeAct agent Docker deployment

echo "🚀 Testing CodeAct Agent Docker Deployment"

# Build the container
echo "Building container..."
docker compose build

# Start the service
echo "Starting CodeAct agent..."
docker compose up -d

# Wait for service to start
echo "Waiting for service to be ready..."
sleep 10

# Test health endpoint
echo "Testing health endpoint..."
curl -f http://localhost:8000/health
echo

# Test agent card
echo "Testing agent card..."
curl -s http://localhost:8000/rpc/.well-known/agent.json | jq .
echo

# Test with Python client
echo "Testing with Python client..."
cd tests && python test_client.py

echo "✅ Docker test complete"
