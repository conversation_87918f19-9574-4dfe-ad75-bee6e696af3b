#!/usr/bin/env python3
"""Test package installation functionality."""

import asyncio
import sys
import os

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from codeact_graph import build_agent
from langchain_core.runnables import RunnableConfig

async def test_package_installation():
    """Test dynamic package installation."""
    print("🧪 Testing Package Installation")
    print("=" * 40)
    
    agent, _ = await build_agent()
    config = {"configurable": {"thread_id": "package-test"}}
    
    # Test 1: Install Python package
    print("\n[TEST] Installing Python package...")
    messages = [{
        "role": "user", 
        "content": "!pip install requests\nimport requests\nprint(f'Requests version: {requests.__version__}')"
    }]
    
    for typ, chunk in agent.stream(
        {"messages": messages},
        stream_mode=["values", "messages"],
        config=config,
    ):
        if typ == "messages":
            print(chunk[0].content, end="")
    
    # Test 2: Install conda package
    print("\n\n[TEST] Installing conda package...")
    messages = [{
        "role": "user",
        "content": "!conda install scipy -y\nimport scipy\nprint(f'SciPy version: {scipy.__version__}')"
    }]
    
    for typ, chunk in agent.stream(
        {"messages": messages},
        stream_mode=["values", "messages"],
        config=config,
    ):
        if typ == "messages":
            print(chunk[0].content, end="")
    
    # Test 3: Install R package (if R is available)
    print("\n\n[TEST] Installing R package...")
    messages = [{
        "role": "user",
        "content": 'install.packages("jsonlite")\nlibrary(jsonlite)\ncat("R jsonlite package loaded successfully\\n")'
    }]
    
    for typ, chunk in agent.stream(
        {"messages": messages},
        stream_mode=["values", "messages"],
        config=config,
    ):
        if typ == "messages":
            print(chunk[0].content, end="")

if __name__ == "__main__":
    asyncio.run(test_package_installation())
