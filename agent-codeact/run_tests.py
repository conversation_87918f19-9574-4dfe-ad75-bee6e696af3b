#!/usr/bin/env python3
"""
Unified test runner for all CodeAct agent tests.
"""

import subprocess
import sys
import os
import argparse

def run_all_tests():
    """Run all tests in the tests directory."""
    print("🧪 Running All CodeAct Tests")
    print("=" * 40)
    
    # Set environment for tests
    os.environ["CODEACT_CONDA_ENV"] = "codeact-test"
    
    # Run pytest with all tests
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "tests/", "-v", "--tb=short", "-x"
    ], capture_output=False)
    
    return result.returncode == 0

def run_env_tests():
    """Run environment-specific tests."""
    print("🧪 Running Environment Tests")
    print("=" * 40)
    
    os.environ["CODEACT_CONDA_ENV"] = "codeact-test"
    
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "tests/test_comprehensive_env.py",
        "tests/test_simple_packages.py",
        "-v", "--tb=short"
    ], capture_output=False)
    
    return result.returncode == 0

def run_a2a_tests():
    """Run A2A protocol tests."""
    print("🧪 Running A2A Protocol Tests")
    print("=" * 40)
    print("Note: Requires server running at localhost:8000")
    
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "tests/test_client.py",
        "tests/test_variable_persistance.py",
        "tests/test_mcp_filesystem.py",
        "-v", "--tb=short"
    ], capture_output=False)
    
    return result.returncode == 0

def run_package_tests():
    """Run package installation tests."""
    print("🧪 Running Package Installation Tests")
    print("=" * 40)
    
    os.environ["CODEACT_CONDA_ENV"] = "codeact-test"
    
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "tests/test_package_install.py",
        "tests/test_simple_packages.py",
        "-v", "--tb=short"
    ], capture_output=False)
    
    return result.returncode == 0

def run_docker_tests():
    """Run Docker integration tests."""
    print("🧪 Running Docker Integration Tests")
    print("=" * 40)
    
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "tests/test_docker_integration.py",
        "-v", "--tb=short"
    ], capture_output=False)
    
    return result.returncode == 0

def main():
    parser = argparse.ArgumentParser(description="CodeAct Test Runner")
    parser.add_argument(
        "suite", 
        nargs="?", 
        choices=["all", "env", "a2a", "packages", "docker"],
        default="all",
        help="Test suite to run (default: all)"
    )
    
    args = parser.parse_args()
    
    test_functions = {
        "all": run_all_tests,
        "env": run_env_tests,
        "a2a": run_a2a_tests,
        "packages": run_package_tests,
        "docker": run_docker_tests,
    }
    
    success = test_functions[args.suite]()
    
    print(f"\n{'✅' if success else '❌'} {args.suite.title()} tests {'passed' if success else 'failed'}")
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
