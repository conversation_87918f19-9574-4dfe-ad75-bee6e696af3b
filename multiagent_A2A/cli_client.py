#!/usr/bin/env python
"""
Simple CLI client for the Multi-Agent A2A system.
"""

import sys
import asyncio
import uuid
import httpx
import json
from typing import Dict, Any, List, Optional

# Default port if not specified
DEFAULT_PORT = 8100

async def send_message(message: str, port: int = DEFAULT_PORT) -> Dict[str, Any]:
    """Send a message to the agent and wait for the response."""
    url = f"http://localhost:{port}/a2a/tasks/send"
    
    # Create a unique context ID for this conversation
    context_id = str(uuid.uuid4())
    
    payload = {
        "message": {
            "role": "user",
            "parts": [{"text": message}],
            "contextId": context_id,
            "messageId": str(uuid.uuid4()),
            "metadata": {"user_id": "cli_user"}
        }
    }
    
    print(f"\n[Sending to agent on port {port}]: {message}")
    print("Waiting for response...")
    
    async with httpx.AsyncClient(timeout=120.0) as client:
        response = await client.post(url, json=payload)
        
        if response.status_code != 200:
            print(f"Error: {response.status_code} - {response.text}")
            return {"error": response.text}
        
        task_data = response.json()
        task_id = task_data.get("id")
        
        if not task_id:
            return {"error": "No task ID returned"}
        
        # Poll for completion
        stream_url = f"http://localhost:{port}/a2a/tasks/{task_id}/stream"
        
        async with client.stream("GET", stream_url) as stream:
            async for line in stream.aiter_lines():
                if not line.strip() or line.startswith(":"):
                    continue
                
                try:
                    event_data = json.loads(line.replace("data: ", ""))
                    
                    # Handle status updates
                    if event_data.get("type") == "task.status.update":
                        status = event_data.get("status", {})
                        state = status.get("state")
                        
                        # Print agent messages
                        if status.get("message"):
                            message_parts = status.get("message", {}).get("parts", [])
                            for part in message_parts:
                                if part.get("text"):
                                    print(f"\n[Agent]: {part.get('text')}")
                        
                        # Check if task is complete
                        if state in ["completed", "failed", "canceled"]:
                            print(f"\n[Task {state}]")
                            break
                    
                    # Handle artifact updates
                    elif event_data.get("type") == "task.artifact.update":
                        artifact = event_data.get("artifact", {})
                        for part in artifact.get("parts", []):
                            if part.get("text"):
                                print(f"\n[Result]: {part.get('text')}")
                
                except json.JSONDecodeError:
                    print(f"Error parsing event: {line}")
                except Exception as e:
                    print(f"Error processing event: {e}")
        
        return {"success": True}

async def main():
    """Main function to run the CLI client."""
    # Get port from command line argument
    port = DEFAULT_PORT
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"Invalid port: {sys.argv[1]}. Using default port {DEFAULT_PORT}.")
    
    print(f"Multi-Agent A2A CLI Client (connecting to port {port})")
    print("Type 'exit' or 'quit' to exit.")
    print("=" * 50)
    
    while True:
        try:
            user_input = input("\nYou: ")
            
            if user_input.lower() in ["exit", "quit"]:
                print("Goodbye!")
                break
            
            if not user_input.strip():
                continue
            
            await send_message(user_input, port)
            
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
