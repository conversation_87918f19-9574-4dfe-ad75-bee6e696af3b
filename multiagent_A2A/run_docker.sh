#!/bin/bash
# Docker-based deployment script

echo "Multi-Agent A2A System - Docker Deployment"
echo "========================================="

# Check prerequisites
if ! command -v docker &> /dev/null; then
    echo "Error: Docker not installed"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "Error: docker-compose not installed"
    exit 1
fi

if [ -z "$GOOGLE_API_KEY" ]; then
    echo "Error: GOOGLE_API_KEY not set"
    exit 1
fi

# Create .env file for docker-compose
cat > .env << EOF
GOOGLE_API_KEY=$GOOGLE_API_KEY
EOF

# Build and start services
echo "Building and starting services..."
docker-compose up --build -d

# Wait for services to be ready
echo "Waiting for services to start..."
sleep 10

# Check service health
echo ""
echo "Checking service status:"
docker-compose ps

echo ""
echo "Testing agent availability:"
curl -s http://localhost:8100/.well-known/agent.json | jq -r '.name' 2>/dev/null && echo "✓ Manager agent ready" || echo "✗ Manager agent not ready"
curl -s http://localhost:8101/.well-known/agent.json | jq -r '.name' 2>/dev/null && echo "✓ CodeAct agent ready" || echo "✗ CodeAct agent not ready"
curl -s http://localhost:8102/.well-known/agent.json | jq -r '.name' 2>/dev/null && echo "✓ Researcher agent ready" || echo "✗ Researcher agent not ready"

echo ""
echo "View logs with: docker-compose logs -f"
echo "Stop with: docker-compose down"
