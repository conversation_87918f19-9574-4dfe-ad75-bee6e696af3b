#!/bin/bash
# Development script to run agents locally without Docker

echo "Starting Multi-Agent A2A System (Development Mode)"
echo "================================================"

# Check environment
if [ -z "$GOOGLE_API_KEY" ]; then
    echo "Error: GOOGLE_API_KEY not set"
    exit 1
fi

# Activate conda environment
source /Users/<USER>/opt/anaconda3/etc/profile.d/conda.sh
conda activate py313_langgraphA2A

# Set Python path
export PYTHONPATH="/Users/<USER>/Documents/Startup/codex/multiagent_A2A:/Users/<USER>/Documents/Startup/codex/multiagent_A2A/a2a-python/src:$PYTHONPATH"

# Kill any existing processes on our ports
echo "Cleaning up existing processes..."
lsof -ti:8100 | xargs kill -9 2>/dev/null
lsof -ti:8101 | xargs kill -9 2>/dev/null
lsof -ti:8102 | xargs kill -9 2>/dev/null

# Start CodeAct agent
echo "Starting CodeAct agent on port 8101..."
cd /Users/<USER>/Documents/Startup/codex/agent-codeact

# Create CodeAct MCP filesystem and temp directories if they don't exist
mkdir -p /Users/<USER>/Documents/Startup/codex/multiagent_A2A/mcp_data/codeact_fs
mkdir -p /Users/<USER>/Documents/Startup/codex/multiagent_A2A/mcp_data/codeact_tmp

# Set environment variables for CodeAct MCP paths
export CODEACT_MCP_FILESYSTEM_PATH="/Users/<USER>/Documents/Startup/codex/multiagent_A2A/mcp_data/codeact_fs"
export CODEACT_MCP_TMP_PATH="/Users/<USER>/Documents/Startup/codex/multiagent_A2A/mcp_data/codeact_tmp"

# Copy updated files if they don't exist
cp -n codeact_graph_update.py codeact_graph.py 2>/dev/null || true
cp -n codeact_a2a_server_update.py codeact_a2a_server.py 2>/dev/null || true

# Start CodeAct agent with updated configuration
python codeact_a2a_server.py &
CODEACT_PID=$!

# Give CodeAct time to start
sleep 5

# Start Manager agent
echo "Starting Manager agent on port 8100..."
cd /Users/<USER>/Documents/Startup/codex/multiagent_A2A
export MCP_CONFIG_PATH_MANAGER="/Users/<USER>/Documents/Startup/codex/multiagent_A2A/agents/manager/config/config_mcp.json"
python -m agents.manager.server &
MANAGER_PID=$!

# Start Researcher agent
echo "Starting Researcher agent on port 8102..."
cd /Users/<USER>/Documents/Startup/codex/multiagent_A2A

# Create Researcher MCP filesystem directory if it doesn't exist
mkdir -p /Users/<USER>/Documents/Startup/codex/multiagent_A2A/mcp_data/researcher_fs

# Set environment variables for Researcher
export MCP_CONFIG_PATH_RESEARCHER="/Users/<USER>/Documents/Startup/codex/multiagent_A2A/agents/researcher/config/config_mcp.json"

# Start Researcher agent
python -m agents.researcher.server &
RESEARCHER_PID=$!

echo ""
echo "Agents started:"
echo "- Manager: http://localhost:8100 (PID: $MANAGER_PID)"
echo "- CodeAct: http://localhost:8101 (PID: $CODEACT_PID)"
[ ! -z "$RESEARCHER_PID" ] && echo "- Researcher: http://localhost:8102 (PID: $RESEARCHER_PID)"

echo ""
echo "Press Ctrl+C to stop all agents"

# Function to cleanup on exit
cleanup() {
    echo "Stopping agents..."
    kill $MANAGER_PID 2>/dev/null
    kill $CODEACT_PID 2>/dev/null
    [ ! -z "$RESEARCHER_PID" ] && kill $RESEARCHER_PID 2>/dev/null
    exit 0
}

trap cleanup INT TERM

# Wait for interrupt
wait
