FROM python:3.13-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl ca-certificates git gnupg unzip \
    && rm -rf /var/lib/apt/lists/*

# Install uv and Node.js for MCP tools
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:${PATH}"

RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    corepack enable

# Copy requirements and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create directories for shared data
RUN mkdir -p /workspace /app/data/conversations

# Expose port
EXPOSE 8100

CMD ["python", "-m", "agents.manager.server"]