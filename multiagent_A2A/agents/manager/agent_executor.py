import logging
import async<PERSON>
import uuid
from typing import Any, Dict

from a2a.server.agent_execution.agent_executor import Agent<PERSON>xecutor
from a2a.server.agent_execution.context import RequestContext
from a2a.server.events.event_queue import EventQueue
# Importing specific event types and TaskStatus
# Removed NewMessageEvent as it's not in a2a.types
from a2a.types import Task, TaskState, TaskStatus, Message, Role, TextPart, Artifact, TaskStatusUpdateEvent, TaskArtifactUpdateEvent 
# new_task from a2a.utils likely re-exports a2a.utils.task.new_task
from a2a.utils import new_task 
from a2a.utils.message import new_agent_text_message
from a2a.utils.artifact import new_text_artifact, new_data_artifact

from .logic.mcp_agent import ManagerAgent

logger = logging.getLogger(__name__)

class ManagerAgentExecutor(AgentExecutor):
    """
    AgentExecutor for the Manager Agent, integrating with the a2a-python library.
    """

    def __init__(self, api_key: str, mcp_config_path: str):
        """
        Initializes the ManagerAgentExecutor.

        Args:
            api_key: Google API key for the ManagerAgent.
            mcp_config_path: Path to the MCP configuration file for the ManagerAgent.
        """
        super().__init__()
        self.agent = ManagerAgent(api_key=api_key, mcp_config_path=mcp_config_path)
        logger.info("ManagerAgentExecutor Initialized with ManagerAgent.")

    async def execute(self, context: RequestContext, event_queue: EventQueue) -> None:
        """
        Executes the agent's logic based on the request context and sends events
        via the event queue.

        Args:
            context: The request context containing information about the task.
            event_queue: The event queue to send updates and results.
        """
        
        if not context.message:
            logger.error("ManagerAgentExecutor: No message provided in RequestContext.")
            return

        task = context.current_task
        if not task:
            task = new_task(context.message) 
            event_queue.enqueue_event(task) 
            logger.info(f"Task {task.id} created for context {task.contextId} (Manager).")
        
        task_id = task.id
        context_id = task.contextId

        try:
            input_text = context.get_user_input() 
            
            if not input_text:
                logger.warning(f"Task {task_id} (Manager): No text input found.")
                status_update_event = TaskStatusUpdateEvent(
                    taskId=task_id,
                    contextId=context_id,
                    status=TaskStatus(
                        state=TaskState.failed, 
                        message=new_agent_text_message(
                            "No text input provided to the manager agent.",
                            context_id,
                            task_id
                        )
                    ),
                    final=True
                )
                event_queue.enqueue_event(status_update_event) 
                return

            logger.info(f"Task {task_id} (Manager): Processing input: '{input_text[:100]}...'")

            user_id = "anon_a2a_user" 
            if context.message and context.message.metadata and "user_id" in context.message.metadata:
                user_id = context.message.metadata["user_id"]
            
            agent_input_data = {
                "input": input_text,
                "metadata": {
                    "user_id": user_id,
                    "a2a_task_id": task_id,
                    "a2a_context_id": context_id,
                }
            }

            working_status_event = TaskStatusUpdateEvent(
                taskId=task_id,
                contextId=context_id,
                status=TaskStatus(
                    state=TaskState.working, 
                    message=new_agent_text_message(
                        "Manager is analyzing the request...",
                        context_id,
                        task_id
                    )
                ),
                final=False
            )
            event_queue.enqueue_event(working_status_event) 

            action_description = "Manager is processing the request."
            if self.agent.should_delegate_to_codeact(input_text):
                action_description = f"Manager is preparing to delegate task to SWE agent: {input_text}"
            elif self.agent.should_delegate_to_researcher(input_text):
                action_description = f"Manager is preparing to delegate task to Researcher agent: {input_text}"
            else:
                action_description = f"Manager is handling task directly: {input_text}"
            
            action_status_event = TaskStatusUpdateEvent(
                taskId=task_id,
                contextId=context_id,
                status=TaskStatus(
                    state=TaskState.working, 
                    message=new_agent_text_message(
                        action_description, 
                        context_id,
                        task_id
                    )
                ),
                final=False 
            )
            event_queue.enqueue_event(action_status_event) 

            result_dict = await self.agent.run(agent_input_data)
            if result_dict.get("status") == "input_required":
                input_required_status_event = TaskStatusUpdateEvent(
                    taskId=task_id,
                    contextId=context_id,
                    status=TaskStatus(
                        state=TaskState.input_required,
                        message=new_agent_text_message(
                            result_dict.get("output", "Agent requires user input."),
                            context_id,
                            task_id
                        )
                    ),
                    final=True
                )
                event_queue.enqueue_event(input_required_status_event)
                return

            # Continue with existing success handling...
            output_text = result_dict.get("output", "No output from agent.")
            
            result_artifact = new_text_artifact(
                name="manager_result",
                description="Result of manager agent processing.",
                text=output_text, # Corrected from text_content to text
            )
            event_queue.enqueue_event(TaskArtifactUpdateEvent( 
                taskId=task_id,
                contextId=context_id,
                artifact=result_artifact,
                append=False, 
                lastChunk=True 
            ))
            logger.info(f"Task {task_id} (Manager): Sent result artifact.")

            completed_status_event = TaskStatusUpdateEvent(
                taskId=task_id,
                contextId=context_id,
                status=TaskStatus(
                    state=TaskState.completed, 
                    message=new_agent_text_message( 
                        "Manager task completed successfully. Result sent as artifact.",
                        context_id,
                        task_id
                    )
                ),
                final=True
            )
            event_queue.enqueue_event(completed_status_event) 
            logger.info(f"Task {task_id} (Manager): Status updated to COMPLETED.")

        except Exception as e:
            logger.exception(f"Task {task_id} (Manager): Error during execution: {e}")
            error_message_text = f"An error occurred in ManagerAgent: {str(e)}"
            failure_status_event = TaskStatusUpdateEvent(
                taskId=task_id,
                contextId=context_id,
                status=TaskStatus(
                    state=TaskState.failed, 
                    message=new_agent_text_message(
                        error_message_text,
                        context_id,
                        task_id
                    )
                ),
                final=True
            )
            event_queue.enqueue_event(failure_status_event) 
        finally:
            pass

    async def cancel(self, context: RequestContext, event_queue: EventQueue) -> None:
        task_id = context.task_id
        context_id = context.context_id 
        logger.warning(f"Task {task_id} (Manager): Cancellation requested. Manager agent does not currently support task cancellation.")
        raise NotImplementedError("Task cancellation is not implemented for ManagerAgentExecutor.")
