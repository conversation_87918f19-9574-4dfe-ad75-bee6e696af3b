"""
Updated server.py with Agent Card endpoint and smart routing
"""
import os
import logging
import json
import httpx
import uvicorn
from starlette.responses import JSONResponse

from a2a.server.apps import A2AStarletteApplication
from a2a.server.request_handlers.default_request_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON>
from a2a.server.tasks import InMemoryTaskStore, InMemoryPushNotifier
from a2a.types import (
    AgentCard, AgentSkill, AgentCapabilities, AgentProvider
)

from .agent_executor import ManagerAgentExecutor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_agent_card(host: str, port: int) -> AgentCard:
    """Create Agent Card dynamically"""
    return AgentCard(
        name="SmartRoutingManager",
        description="Intelligent manager with smart routing, A2A discovery, and MCP capabilities",
        url=f"http://{host}:{port}",
        version="1.0.0",
        capabilities=AgentCapabilities(
            streaming=False,
            pushNotifications=False,
            stateTransitionHistory=True
        ),
        skills=[
            AgentSkill(
                id="smart_routing",
                name="Smart Routing",
                description="Route requests to optimal specialized agents",
                tags=["routing", "coordination"]
            ),
            AgentSkill(
                id="agent_discovery",
                name="Agent Discovery", 
                description="Discover and coordinate with A2A agents",
                tags=["discovery", "a2a"]
            ),
            AgentSkill(
                id="task_coordination",
                name="Task Coordination",
                description="Coordinate complex multi-agent tasks",
                tags=["coordination", "delegation"]
            ),
            AgentSkill(
                id="mcp_tools",
                name="MCP Tools",
                description="Access to Model Context Protocol tools",
                tags=["mcp", "tools", "filesystem"]
            )
        ],
        defaultInputModes=["text/plain"],
        defaultOutputModes=["text/plain"],
        provider=AgentProvider(
            organization="MultiAgent A2A",
            url="https://github.com/google/A2A"
        )
    )

# Environment setup
GOOGLE_API_KEY = os.environ.get("GOOGLE_API_KEY")
if not GOOGLE_API_KEY:
    logger.warning("GOOGLE_API_KEY not set")

DEFAULT_MCP_CONFIG_PATH = os.path.join(os.path.dirname(__file__), "config", "config_mcp.json")
MCP_CONFIG_PATH = os.environ.get("MCP_CONFIG_PATH_MANAGER", DEFAULT_MCP_CONFIG_PATH)

HOST = os.environ.get("MANAGER_AGENT_HOST", "0.0.0.0")
PORT = int(os.environ.get("MANAGER_AGENT_PORT", "8100"))

# Create components
manager_agent_card = create_agent_card(HOST, PORT)
manager_agent_executor = ManagerAgentExecutor(
    api_key=GOOGLE_API_KEY,
    mcp_config_path=MCP_CONFIG_PATH
)

task_store = InMemoryTaskStore()
async_http_client = httpx.AsyncClient()
push_notifier = InMemoryPushNotifier(httpx_client=async_http_client)

request_handler = DefaultRequestHandler(
    agent_executor=manager_agent_executor,
    task_store=task_store,
    push_notifier=push_notifier
)

# Create A2A application
application = A2AStarletteApplication(
    agent_card=manager_agent_card,
    http_handler=request_handler
)

# Get base app
app = application.build(rpc_url="/rpc") # Ensure A2A RPC endpoint is at /rpc

# Add Agent Card endpoint
@app.route("/.well-known/agent.json", methods=["GET"])
async def agent_card_endpoint(request):
    """Serve Agent Card for discovery"""
    # Get dynamic agent card with MCP tools
    if hasattr(manager_agent_executor.agent, 'get_agent_card'):
        await manager_agent_executor.agent._initialize_agent()
        dynamic_card = manager_agent_executor.agent.get_agent_card()
        return JSONResponse(dynamic_card)
    
    # Fallback to static card
    return JSONResponse(manager_agent_card.model_dump())

# Add discovery endpoints
@app.route("/api/discover", methods=["POST"])
async def discover_agents(request):
    """Trigger agent discovery"""
    try:
        body = await request.json()
        agent_urls = body.get("agent_urls", [])
        
        await manager_agent_executor.agent._initialize_agent()
        
        discovered_count = 0
        for url in agent_urls:
            agent = await manager_agent_executor.agent.discovery_service.discover_agent_by_url(url)
            if agent:
                manager_agent_executor.agent.discovery_service.register_agent(agent)
                discovered_count += 1
        
        return JSONResponse({
            "discovered": discovered_count,
            "total_agents": len(manager_agent_executor.agent.discovery_service.discovered_agents),
            "agents": list(manager_agent_executor.agent.discovery_service.discovered_agents.keys())
        })
    except Exception as e:
        return JSONResponse({"error": str(e)}, status_code=400)

@app.route("/api/agents", methods=["GET"])  
async def list_agents(request):
    """List discovered agents"""
    await manager_agent_executor.agent._initialize_agent()
    
    agents = {}
    for name, agent in manager_agent_executor.agent.discovery_service.discovered_agents.items():
        agents[name] = {
            "name": agent.name,
            "description": agent.description,
            "endpoint": agent.endpoint,
            "capabilities": [
                {"name": cap.name, "description": cap.description}
                for cap in agent.capabilities
            ]
        }
    
    return JSONResponse(agents)

@app.route("/health", methods=["GET"])
async def health_check(request):
    """Health check endpoint"""
    return JSONResponse({"status": "healthy", "service": "manager"})

if __name__ == "__main__":
    logger.info(f"Starting Smart Routing Manager on {HOST}:{PORT}")
    uvicorn.run(app, host=HOST, port=PORT)
