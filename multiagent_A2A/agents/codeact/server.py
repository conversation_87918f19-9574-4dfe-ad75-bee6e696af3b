import os
import sys
from pathlib import Path

# Add CodeAct agent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent.parent / "agent-codeact"))

# Import the build_app function
from codeact_a2a_server import build_app

import uvicorn
from starlette.applications import Starlette
from starlette.routing import Route, Mount
from starlette.responses import JSONResponse

# Get the CodeAct app
codeact_app = build_app()

# Create a wrapper that exposes agent card at root level
async def get_agent_card_root(request):
    """Proxy agent card request to the CodeAct app"""
    # Import the agent card from the CodeAct module
    from codeact_a2a_server import AGENT_CARD
    return JSONResponse(AGENT_CARD.model_dump(mode="json", exclude_none=True))

# Create wrapper app with root-level agent card
app = Starlette(routes=[
    Route("/.well-known/agent.json", get_agent_card_root, methods=["GET"]),
    Mount("/", codeact_app),
])

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8103)