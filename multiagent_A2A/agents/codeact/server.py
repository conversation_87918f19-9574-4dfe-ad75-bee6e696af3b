import os
import sys
from pathlib import Path

# Add CodeAct agent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent.parent / "agent-codeact"))

# Import the build_app function
from codeact_a2a_server import build_app

import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="CodeAct A2A Agent")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount the A2A app
app.mount("/", build_app())

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8103)