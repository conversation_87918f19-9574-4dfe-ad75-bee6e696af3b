import os
import logging
from typing import Dict, Any, Optional

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage

from a2a.server.agent_execution.agent_executor import AgentExecutor
from a2a.server.agent_execution.context import RequestContext
from a2a.server.events.event_queue import EventQueue
from a2a.types import TaskStatusUpdateEvent, Message, TaskState, TaskStatus

logger = logging.getLogger(__name__)

class SimpleResearchAgentExecutor(AgentExecutor):
    """Simple research agent using Google Generative AI."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key or os.environ.get("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError("GOOGLE_API_KEY required")
            
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash",
            api_key=self.api_key,
            temperature=0.1
        )
        
        logger.info("SimpleResearchAgent initialized")
    
    async def execute(self, context: RequestContext, event_queue: EventQueue):
        """Execute research task."""
        try:
            event_queue.enqueue_event(TaskStatusUpdateEvent(
                taskId=context.task_id,
                contextId=context.context_id,
                status=TaskStatus(state=TaskState.working),
                final=False
            ))
            
            # Extract user content
            user_content = ""
            for part in context.message.parts:
                if hasattr(part, 'root'):
                    part_data = part.root
                else:
                    part_data = part
                    
                if hasattr(part_data, 'kind') and part_data.kind == "text":
                    user_content += part_data.text + "\n"
            
            if not user_content.strip():
                event_queue.enqueue_event(TaskStatusUpdateEvent(
                    taskId=context.task_id,
                    contextId=context.context_id,
                    status=TaskStatus(state=TaskState.failed),
                    final=True
                ))
                return
            
            # Add research context to prompt
            research_prompt = f"""You are a research assistant. Based on the following query, provide a helpful response using your knowledge. If you need current information from the web, mention that web search would be helpful.

Query: {user_content.strip()}"""
            
            # Generate response
            response = await self.llm.ainvoke([HumanMessage(content=research_prompt)])
            
            # Send response as Message
            event_queue.enqueue_event(Message(
                messageId=f"resp_{context.task_id}",
                role="agent",
                parts=[{"kind": "text", "text": response.content}],
                contextId=context.context_id,
                taskId=context.task_id
            ))
            
            event_queue.enqueue_event(TaskStatusUpdateEvent(
                taskId=context.task_id,
                contextId=context.context_id,
                status=TaskStatus(state=TaskState.completed),
                final=True
            ))
            
        except Exception as e:
            error_msg = f"Research failed: {str(e)}"
            logger.error(error_msg)
            
            event_queue.enqueue_event(TaskStatusUpdateEvent(
                taskId=context.task_id,
                contextId=context.context_id,
                status=TaskStatus(state=TaskState.failed),
                final=True
            ))
    
    async def cancel(self, context: RequestContext, event_queue: EventQueue):
        """Cancel research task."""
        event_queue.enqueue_event(TaskStatusUpdateEvent(
            taskId=context.task_id,
            contextId=context.context_id,
            status=TaskStatus(state=TaskState.canceled),
            final=True
        ))
