import logging
import json
import os
from typing import List, Dict, Any, Optional

from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder # Added MessagesPlaceholder
from langchain.agents import AgentExecutor as LangchainAgentExecutor, create_react_agent
from langchain_google_genai import ChatGoogleGenerativeA<PERSON>
from langchain import hub # Added for pulling prompts
from langchain_mcp_adapters.client import MultiServerMCPClient

logger = logging.getLogger(__name__)

class ResearcherAgent:
    """Researcher Agent specializing in information gathering, using MultiServerMCPClient."""

    def __init__(self, api_key: str, mcp_config_path: Optional[str] = None, llm_model_name: str = "gemini-2.0-flash"):
        self.api_key = api_key
        self.llm_model_name = llm_model_name
        self.mcp_config_path = mcp_config_path
        
        self.llm = ChatGoogleGenerativeAI(model=self.llm_model_name, google_api_key=self.api_key) # Removed convert_system_message_to_human
        
        self.tools: List[Any] = []
        self.langchain_agent_executor: Optional[LangchainAgentExecutor] = None
        self._initialized = False
        self.allowed_directories = []
        
        # Researcher-specific attributes (were present in old version)
        self.research_sources: List[Dict[str, str]] = []
        self.visited_urls: set[str] = set()

        logger.info(f"ResearcherAgent Initialized (pending async setup). MCP Config Path: {self.mcp_config_path}")

    async def initialize_agent(self): # Renamed from _initialize_agent to match executor calls
        if self._initialized:
            return

        mcp_server_definitions = {}
        if self.mcp_config_path and os.path.exists(self.mcp_config_path):
            try:
                with open(self.mcp_config_path, 'r') as f:
                    config_data = json.load(f)
                    mcp_server_definitions = config_data.get("mcpServers", {})
                    logger.info(f"Researcher Agent: Loaded MCP server definitions: {list(mcp_server_definitions.keys())}")
                    fs_config = mcp_server_definitions.get("filesystem")
                    if fs_config and fs_config.get("args"):
                        self.allowed_directories = [arg for arg in fs_config["args"][1:] if arg.startswith("/")]
                        logger.info(f"Researcher Agent: Allowed directories: {self.allowed_directories}")
            except Exception as e:
                logger.error(f"Researcher Agent: Error loading MCP config from {self.mcp_config_path}: {e}")
        else:
            logger.warning("Researcher Agent: MCP config path not provided or file does not exist.")

        if mcp_server_definitions:
            mcp_client = MultiServerMCPClient(mcp_server_definitions)
            try:
                mcp_tools_list = await mcp_client.get_tools()
                self.tools = [tool for tool in mcp_tools_list if tool is not None]
                logger.info(f"Researcher Agent: Loaded {len(self.tools)} MCP tools: {[tool.name for tool in self.tools]}")
            except Exception as e:
                logger.error(f"Researcher Agent: Error getting tools from MultiServerMCPClient: {e}")
                self.tools = []
        else:
            self.tools = []

        custom_system_message_content = self._get_system_prompt_template_str()

        # Always construct the ChatPromptTemplate to ensure correct structure
        # Incorporate ReAct instructions along with the custom system message.
        prompt = ChatPromptTemplate.from_messages([
            ("system", custom_system_message_content + 
             "\n\nYou are a ReAct agent. You have access to the following tools:\n{tools}\n"
             "Use the following format:\n\n"
             "Question: the input question you must answer\n"
             "Thought: you should always think about what to do\n"
             "Action: the action to take, should be one of [{tool_names}]\n"
             "Action Input: the input to the action\n"
             "Observation: the result of the action\n"
             "... (this Thought/Action/Action Input/Observation can repeat N times)\n"
             "Thought: I now know the final answer\n"
             "Final Answer: the final answer to the original input question\n\n"
             "Begin!"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad") # Correct usage
        ])
        logger.info("Researcher Agent: Using a directly constructed ChatPromptTemplate with ReAct instructions.")
            
        try:
            from langchain.agents.format_scratchpad.openai_tools import format_to_openai_tool_messages
            from langchain.agents.output_parsers import OpenAIToolsAgentOutputParser
            
            agent_runnable = create_react_agent(self.llm, self.tools, prompt)
            self.langchain_agent_executor = LangchainAgentExecutor(
                agent=agent_runnable, 
                tools=self.tools,
                verbose=True,
                handle_parsing_errors=True
            )
            logger.info("Researcher Agent: Langchain AgentExecutor created with proper message formatting.")
        except Exception as e:
            logger.error(f"Researcher Agent: Error creating Langchain AgentExecutor: {e}")
            self.langchain_agent_executor = None

        self._initialized = True

    def _get_system_prompt_template_str(self) -> str: # Renamed
        allowed_dirs_str = "\n".join([f"- {dir}" for dir in self.allowed_directories]) if self.allowed_directories else "N/A (Filesystem tool not configured or no paths specified)"
        
        # This will be prepended to the standard ReAct system message.
        return f"""You are a skilled Researcher Agent specializing in information gathering, analysis, and synthesis.
Your available tools will be listed by the system.

**CRITICAL FILE ACCESS RULES (if Filesystem tool is active):**
- Operate ONLY within these allowed directories:
{allowed_dirs_str}
- ALL file paths MUST start with one of these allowed paths if using filesystem tools.

**Research Guidelines:**
1. Analyze questions thoroughly before answering.
2. Break complex research tasks into subtopics.
3. Provide balanced and comprehensive answers.
4. Cite sources when available (you might need to simulate this if web search tools are not directly available, or use filesystem to save/retrieve findings).
5. Organize information logically.

Be helpful, concise, and efficient.
"""
    # The `research` method was specific to the old ResearcherAgent.
    # The new pattern is to have a generic `run` method that uses the langchain_agent_executor.
    # The specific "research" behavior is now primarily guided by the system prompt.
    # We can keep the source tracking if the Langchain agent's output or tool usage allows for it.
    
    async def run(self, input_data: Any) -> Dict[str, Any]:
        await self.initialize_agent()

        query = input_data.get("input", "") if isinstance(input_data, dict) else str(input_data)
        logger.info(f"ResearcherAgent received query for run: '{query[:100]}...'")
        
        self.clear_sources() # Clear previous research sources for this run

        if not self.langchain_agent_executor:
            logger.error("Researcher Agent's Langchain agent executor not initialized.")
            return {"output": "Error: Researcher agent not properly initialized.", "error": "Agent not initialized", "sources": []}

        try:
            agent_input = {"input": query}
            langchain_response = await self.langchain_agent_executor.ainvoke(agent_input)
            answer = langchain_response.get("output", "No specific output from researcher agent.")

            # Source extraction would depend on how tools report sources or if LLM cites them.
            # For now, this is a placeholder. If tools like a web search tool are used,
            # they might return structured data with sources that can be parsed here.
            # self.add_source(...) would be called if sources are found.
            # Example: if a tool call's observation contains source info:
            # for step in langchain_response.get("intermediate_steps", []):
            # action, observation = step
            # if action.tool == "web_search_tool_name" and isinstance(observation, dict) and "sources" in observation:
            # for src in observation["sources"]: self.add_source(src)
            
            return {
                "output": answer,
                "sources": self.research_sources, # Will be empty unless add_source is called
                "visited_urls": list(self.visited_urls) # Also depends on add_source
            }
        except Exception as e:
            logger.exception(f"Error during ResearcherAgent's execution: {e}")
            return {"output": f"Error during researcher agent's processing: {str(e)}", "error": str(e), "sources": []}

    # Methods for managing sources (can be adapted if tools provide source info)
    def get_research_sources(self) -> List[Dict[str, str]]:
        return self.research_sources

    def get_visited_urls(self) -> List[str]:
        return list(self.visited_urls)

    def add_source(self, source: Dict[str, str]) -> None:
        if source not in self.research_sources:
            self.research_sources.append(source)
            if "url" in source:
                self.visited_urls.add(source["url"])

    def clear_sources(self) -> None:
        self.research_sources = []
        self.visited_urls = set()
