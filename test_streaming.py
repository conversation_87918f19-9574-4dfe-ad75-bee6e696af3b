#!/usr/bin/env python3
"""
Test script to verify A2A streaming functionality.
"""

import asyncio
import json
import httpx
import uuid

async def test_streaming():
    """Test the A2A streaming endpoint directly."""

    # Test data
    test_message = {
        "jsonrpc": "2.0",
        "method": "message/send",
        "id": "test-streaming",
        "params": {
            "message": {
                "messageId": str(uuid.uuid4()),
                "role": "user",
                "parts": [{"kind": "text", "text": "Write a Python function to calculate the factorial of 5 and print the result"}],
                "contextId": "streaming-test"
            }
        }
    }

    print("Testing A2A streaming...")
    print(f"Request: {json.dumps(test_message, indent=2)}")

    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            # Test direct CodeAct agent
            print("\n=== Testing CodeAct Agent Directly ===")
            response = await client.post(
                "http://localhost:8101/rpc/",
                json=test_message,
                headers={"Content-Type": "application/json"}
            )

            print(f"Status: {response.status_code}")
            print(f"Headers: {dict(response.headers)}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"Response: {json.dumps(result, indent=2)}")
                except:
                    print(f"Raw response: {response.text}")
            else:
                print(f"Error response: {response.text}")

        except Exception as e:
            print(f"Error testing CodeAct agent: {e}")

        try:
            # Test through Manager agent
            print("\n=== Testing Manager Agent (with delegation) ===")
            response = await client.post(
                "http://localhost:8100/rpc",
                json=test_message,
                headers={"Content-Type": "application/json"}
            )

            print(f"Status: {response.status_code}")
            print(f"Headers: {dict(response.headers)}")

            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"Response: {json.dumps(result, indent=2)}")
                except:
                    print(f"Raw response: {response.text}")
            else:
                print(f"Error response: {response.text}")

        except Exception as e:
            print(f"Error testing Manager agent: {e}")

if __name__ == "__main__":
    asyncio.run(test_streaming())
